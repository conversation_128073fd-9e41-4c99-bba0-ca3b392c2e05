# frozen_string_literal: true

class ConsultationSummariesController < ApplicationController
  before_action :require_user
  before_action :require_faculty_access
  before_action :set_consultation_summary, only: [:show, :update]

  # GET /consultation_summaries
  def index
    Rails.logger.info("ConsultationSummariesController index 1")

    # Get completed consultation summaries
    @summaries = @current_user.faculty_consultation_summaries.preload(:student, :consultation_request)
                            .order(consultation_date: :desc)

    Rails.logger.info("ConsultationSummariesController index 2 @summaries: #{@summaries.length}")

    # Get pending and approved consultation requests (not yet completed)
    @pending_requests = @current_user.faculty_consultation_requests
                                   .where(status: ['pending', 'approved'])
                                   .preload(:student, :faculty_time_slot)
                                   .order(preferred_datetime: :desc)

    Rails.logger.info("ConsultationSummariesController index 2.5 @pending_requests: #{@pending_requests.length}")

    # Apply filters to summaries
    @summaries = @summaries.by_concern_type(params[:concern_type]) if params[:concern_type].present?
    @summaries = @summaries.search_by_student(params[:student_search]) if params[:student_search].present?
    @summaries = @summaries.search_by_content(params[:content_search]) if params[:content_search].present?

    # Apply similar filters to pending requests
    if params[:concern_type].present?
      @pending_requests = @pending_requests.where(nature_of_concern: params[:concern_type])
    end
    if params[:student_search].present?
      @pending_requests = @pending_requests.where(
        'student_name ILIKE ? OR student_id ILIKE ?',
        "%#{params[:student_search]}%", "%#{params[:student_search]}%"
      )
    end
    if params[:content_search].present?
      @pending_requests = @pending_requests.where(
        'description ILIKE ? OR faculty_comment ILIKE ?',
        "%#{params[:content_search]}%", "%#{params[:content_search]}%"
      )
    end

    # Apply status filter
    if params[:status].present?
      case params[:status]
      when 'completed'
        # Only show completed summaries, no pending requests
        @pending_requests = @pending_requests.none
      when 'pending'
        # Only show pending requests, no completed summaries
        @pending_requests = @pending_requests.where(status: 'pending')
        @summaries = @summaries.none
      when 'approved'
        # Only show approved requests, no completed summaries
        @pending_requests = @pending_requests.where(status: 'approved')
        @summaries = @summaries.none
      end
    end

    Rails.logger.info("ConsultationSummariesController index 3 @summaries: #{@summaries.length}")

    if params[:start_date].present? && params[:end_date].present?
      start_date = Date.parse(params[:start_date]) rescue nil
      end_date = Date.parse(params[:end_date]) rescue nil
      @summaries = @summaries.in_date_range(start_date, end_date) if start_date && end_date

      # Apply date filter to pending requests using preferred_datetime
      if start_date && end_date
        @pending_requests = @pending_requests.where(
          preferred_datetime: start_date.beginning_of_day..end_date.end_of_day
        )
      end
    end

    Rails.logger.info("ConsultationSummariesController index 4 @summaries: #{@summaries.length}")

    # Pagination
    page = params[:page]&.to_i || 1
    per_page = params[:per_page]&.to_i || 20
    per_page = [per_page, 100].min # Max 100 per page

    @summaries = @summaries.limit(per_page).offset((page - 1) * per_page)
    @pending_requests = @pending_requests.limit(per_page).offset((page - 1) * per_page)

    Rails.logger.info("ConsultationSummariesController index 5 @summaries: #{@summaries.length}")

    respond_to do |format|
      format.json { render json: consultation_summaries_with_requests_json(@summaries, @pending_requests) }
      format.html { render_consultation_summaries_page }
      format.csv { send_csv_export }
    end
  end

  # GET /consultation_summaries/:id
  def show
    respond_to do |format|
      format.json { render json: consultation_summary_json(@consultation_summary) }
    end
  end

  # PATCH/PUT /consultation_summaries/:id
  def update
    if @consultation_summary.update(consultation_summary_params)
      respond_to do |format|
        format.json { render json: consultation_summary_json(@consultation_summary) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @consultation_summary.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # GET /consultation_summaries/dashboard
  def dashboard
    @statistics = @current_user.consultation_statistics
    @concern_type_breakdown = ConsultationSummary.grouped_by_concern_type_for_faculty(@current_user)
    @recent_summaries = @current_user.faculty_consultation_summaries.recent.limit(10)
    @summaries_requiring_follow_up = @current_user.faculty_consultation_summaries.requiring_follow_up.recent.limit(5)

    respond_to do |format|
      format.json { 
        render json: {
          statistics: @statistics,
          concern_type_breakdown: @concern_type_breakdown,
          recent_summaries: consultation_summaries_json(@recent_summaries)[:summaries],
          follow_up_required: consultation_summaries_json(@summaries_requiring_follow_up)[:summaries]
        }
      }
      format.html { render_dashboard_page }
    end
  end

  # GET /consultation_summaries/reports
  def reports
    @year = params[:year]&.to_i || Date.current.year
    @monthly_data = ConsultationSummary.monthly_summary_for_faculty(@current_user, @year)
    @concern_type_stats = ConsultationSummary.grouped_by_concern_type_for_faculty(@current_user)
    @total_consultations = @current_user.faculty_consultation_summaries.count

    respond_to do |format|
      format.json { 
        render json: {
          year: @year,
          monthly_data: @monthly_data,
          concern_type_stats: @concern_type_stats,
          total_consultations: @total_consultations
        }
      }
      format.html { render_reports_page }
    end
  end

  # POST /consultation_summaries/:id/add_notes
  def add_notes
    @consultation_summary = @current_user.faculty_consultation_summaries.find(params[:id])
    notes = params[:notes]

    if notes.present?
      @consultation_summary.add_faculty_notes!(notes)
      respond_to do |format|
        format.json { render json: consultation_summary_json(@consultation_summary) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: ['Notes cannot be blank'] }, status: :unprocessable_entity }
      end
    end
  end

  private

  def require_faculty_access
    unless @current_user.teacher_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Faculty access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Faculty access required.'
          redirect_to root_path
        }
      end
    end
  end

  def set_consultation_summary
    @consultation_summary = @current_user.faculty_consultation_summaries.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: 'Consultation summary not found' }, status: :not_found }
      format.html {
        flash[:error] = 'Consultation summary not found'
        redirect_to consultation_summaries_path
      }
    end
  end

  def consultation_summary_params
    params.require(:consultation_summary).permit(
      :faculty_notes, :outcome_summary, :referral_made, :follow_up_required
    )
  end

  def consultation_summary_json(summary)
    {
      id: summary.id,
      student_name: summary.student_name,
      student_id: summary.student_number,
      consultation_date: summary.consultation_date.iso8601,
      formatted_date: summary.formatted_consultation_date,
      concern_type: summary.concern_type,
      concern_type_display: summary.concern_type_display,
      description: summary.description,
      faculty_notes: summary.faculty_notes,
      outcome_summary: summary.outcome_summary,
      referral_made: summary.referral_made,
      follow_up_required: summary.follow_up_required,
      has_referral: summary.has_referral?,
      requires_follow_up: summary.requires_follow_up?,
      duration_display: summary.duration_display,
      created_at: summary.created_at.iso8601,
      updated_at: summary.updated_at.iso8601
    }
  end

  def consultation_summaries_json(summaries)
    {
      summaries: summaries.map { |summary| consultation_summary_json(summary) },
      total_count: summaries.respond_to?(:count) ? summaries.count : summaries.size
    }
  end

  def consultation_request_json(request)
    {
      id: request.id,
      student_name: request.student_name,
      student_id: request.student_id,
      preferred_datetime: request.preferred_datetime.iso8601,
      consultation_date: request.preferred_datetime.iso8601, # Add this for sorting compatibility
      formatted_date: request.formatted_preferred_datetime,
      concern_type: request.nature_of_concern,
      concern_type_display: request.concern_type_display,
      description: request.description,
      faculty_notes: request.faculty_comment,
      outcome_summary: nil,
      referral_made: nil,
      follow_up_required: nil,
      has_referral: false,
      requires_follow_up: false,
      duration_display: "#{request.duration_in_minutes} minutes",
      status: request.status,
      status_display: request.status_display,
      can_be_completed: request.can_be_completed?,
      created_at: request.created_at.iso8601,
      updated_at: request.updated_at.iso8601,
      type: 'consultation_request' # To distinguish from completed summaries
    }
  end

  def consultation_summaries_with_requests_json(summaries, pending_requests)
    completed_summaries = summaries.map { |summary| consultation_summary_json(summary).merge(type: 'consultation_summary') }
    pending_items = pending_requests.map { |request| consultation_request_json(request) }

    # Combine and sort by date (newest first)
    all_items = (completed_summaries + pending_items).sort_by do |item|
      # Handle potential nil consultation_date values
      date_string = item[:consultation_date]
      date_string.present? ? Date.parse(date_string) : Date.new(1970, 1, 1)
    end.reverse

    {
      summaries: completed_summaries,
      pending_requests: pending_items,
      all_items: all_items,
      total_summaries: summaries.respond_to?(:count) ? summaries.count : summaries.size,
      total_pending: pending_requests.respond_to?(:count) ? pending_requests.count : pending_requests.size,
      total_count: all_items.size
    }
  end

  def send_csv_export
    summaries = @current_user.faculty_consultation_summaries.preload(:student, :consultation_request)

    # Apply same filters as index
    summaries = summaries.by_concern_type(params[:concern_type]) if params[:concern_type].present?
    summaries = summaries.search_by_student(params[:student_search]) if params[:student_search].present?
    summaries = summaries.search_by_content(params[:content_search]) if params[:content_search].present?

    if params[:start_date].present? && params[:end_date].present?
      start_date = Date.parse(params[:start_date]) rescue nil
      end_date = Date.parse(params[:end_date]) rescue nil
      summaries = summaries.in_date_range(start_date, end_date) if start_date && end_date
    end

    # Apply status filter for CSV export
    if params[:status].present? && params[:status] != 'completed'
      # For non-completed status, return empty CSV since we only export completed summaries
      summaries = summaries.none
    end

    csv_data = ConsultationSummary.to_csv(summaries)
    filename = "consultation_summaries_#{Date.current.strftime('%Y%m%d')}.csv"

    send_data csv_data,
              filename: filename,
              type: 'text/csv',
              disposition: 'attachment'
  end

  def render_consultation_summaries_page
    @page_title = 'Consultation Summaries'
    js_env({
      CONSULTATION_SUMMARIES: {
        current_user_id: @current_user.id,
        summaries: consultation_summaries_json(@summaries)[:summaries],
        concern_types: ConsultationSummary::CONCERN_TYPES,
        filters: {
          concern_type: params[:concern_type],
          student_search: params[:student_search],
          content_search: params[:content_search],
          start_date: params[:start_date],
          end_date: params[:end_date],
          status: params[:status]
        }
      }
    })

    js_bundle :consultation_summaries
    css_bundle :consultation_system
    render 'index'
  end

  def render_dashboard_page
    @page_title = 'Consultation Summary Dashboard'
    js_env({
      FACULTY_DASHBOARD: {
        current_user_id: @current_user.id,
        statistics: @statistics,
        pending_requests: [],
        upcoming_consultations: []
      }
    })

    js_bundle :faculty_consultation_dashboard
    css_bundle :consultation_system
    render 'dashboard'
  end

  def render_reports_page
    @page_title = 'Consultation Reports'
    css_bundle :consultation_system
    render 'reports'
  end
end
