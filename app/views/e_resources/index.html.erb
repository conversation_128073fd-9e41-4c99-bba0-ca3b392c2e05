<% provide :page_title do %>E-resources<% end %>

<div class="e-resources-page container">
  <div>
    <h1 class="e-resources-title">E-Resources Directory</h1>
    <p class="intro-text">Explore digital libraries and research materials, categorized by academic disciplines under the College of Agriculture, Forestry, and Environmental Science.</p>
  </div>
  <div class="search-bar">
    <input type="text" id="resourceSearch" placeholder="Search by keyword, discipline, or content type..." onkeyup="filterResources()" class="search-input"/>
  </div>

  <% if @is_admin %>
  <div class="admin-panel" id="adminPanel">
    <div class="admin-header">
      <h2 class="admin-title">
        Administration Panel
        <span class="admin-badge">ADMIN</span>
      </h2>
      <div class="admin-actions">
        <button type="button" class="btn btn-primary" onclick="openAddResourceModal()">
          <i class="icon-plus"></i> Add New Resource
        </button>
        <%= link_to "Export to CSV", export_to_csv_e_resources_path, class: "btn btn-secondary" %>
      </div>
    </div>
    
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-number"><%= @total_count %></span>
          <span class="stat-label">Total Resources</span>
        </div>
        <div class="stat-item">
          <span class="stat-number"><%= @disciplines.count %></span>
          <span class="stat-label">Disciplines</span>
        </div>
        <% @discipline_counts.each do |discipline, count| %>
          <div class="stat-item">
            <span class="stat-number"><%= count %></span>
            <span class="stat-label"><%= discipline %></span>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <% end %>
  
<% @grouped_resources.each do |discipline, resources| %>
  <div class="discipline-section">
    <h2><%= discipline %></h2>
    <ul class="resource-list">
      <% resources.each do |res| %>
        <li class="resource-item" data-tags="<%= res['tags'].join(',') %>">
          <h3>
            <div class="resource-title-section">
              <a href="<%= res['url'] %>" target="_blank" rel="noopener noreferrer">
                <%= res['title'] %>
              </a>
              <% if @is_admin %>
                <div class="resource-admin-actions">
                  <% resource_obj = @e_resources.find { |r| r.title == res['title'] && r.url == res['url'] } %>
                  <% if resource_obj %>
                    <%= link_to "Edit", "#", 
                        class: "btn-small btn-primary", 
                        onclick: "openEditResourceModal(#{resource_obj.id}); return false;" %>
                    <%= link_to "Delete", 
                        e_resource_path(resource_obj), 
                        method: :delete, 
                        class: "btn-small btn-danger", 
                        data: { confirm: "Are you sure you want to delete '#{res['title']}'?" } %>
                  <% end %>
                </div>
              <% end %>
            </div>
          </h3>
          
          <p><strong>Description:</strong> <%= res["description"] %></p>
          
          <div class="resource-details">
            <p><strong>Access:</strong> <%= res['metadata']['access'] %></p>
            <p><strong>Type:</strong> <%= res['metadata']['type'] %></p>
            <p><strong>Updated:</strong> <%= res['metadata']['updated'] %></p>
            <p class="tags">
              <strong>Tags:</strong>
              <% res['tags'].each do |tag| %>
                <span class="tag-badge"><%= tag.gsub('-', ' ').capitalize %></span>
              <% end %>
            </p>
          </div>
        </li>
      <% end %>
    </ul>
  </div>
<% end %>

</div>

<script>
  // Utility Functions
  function filterResources() {
    const input = document.getElementById("resourceSearch").value.toLowerCase();
    const items = document.querySelectorAll(".resource-item");

    items.forEach(item => {
      const text = item.textContent.toLowerCase();
      item.style.display = text.includes(input) ? "block" : "none";
    });
  }

  // Navigation Highlight for E-resources tab
  function updateEresourcesIconHighlight() {
    const eresLink = document.querySelector('#global_nav_eresources_link');
    const eresText = eresLink?.querySelector('.menu-item__text');
    const eresIconPaths = eresLink?.querySelectorAll('svg path');
    const isEresourcesPage = window.location.pathname.startsWith('/e-resources');

    if (!eresLink) return;

    if (isEresourcesPage) {
      // Set active styles
      eresLink.classList.add('ic-app-header__menu-list-item--active');
      eresLink.setAttribute('aria-current', 'page');

      if (eresText) eresText.style.color = '#f4b952';
      if (eresIconPaths) {
        eresIconPaths.forEach(p => p.style.fill = '#f4b952');
      }
    } else {
      // Reset styles
      eresLink.classList.remove('ic-app-header__menu-list-item--active');
      eresLink.removeAttribute('aria-current');
      eresLink.style.backgroundColor = '';

      if (eresText) eresText.style.color = '';
      if (eresIconPaths) {
        eresIconPaths.forEach(p => p.style.fill = '');
      }
    }
  }

  // Watch for SPA route changes
  const observer = new MutationObserver(() => {
    updateEresourcesIconHighlight();
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Initial call on page load
  document.addEventListener('DOMContentLoaded', function () {
    updateEresourcesIconHighlight();
  });

  function isSlidingTabOpen() {
    const slidingTabs = document.querySelectorAll('.ic-app-header__menu-list-item--active');
    return slidingTabs.length > 0;
  }

  // Modal Creation and Management
  function createAddResourceModal() {
    const modalHTML = `
      <div id="addResourceModal" class="canvas-modal-overlay">
        <div class="canvas-modal-dialog">
          <div class="canvas-modal-header">
            <h3 class="canvas-modal-title">Add New E-Resource</h3>
            <button type="button" class="canvas-modal-close" onclick="closeAddResourceModal()">&times;</button>
          </div>
          
          <div class="canvas-modal-body">
            <form action="/e-resources" method="post" id="addResourceForm" class="canvas-resource-form">
              <input type="hidden" name="authenticity_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
              
              <div class="canvas-form-row">
                <label class="canvas-form-label">
                  Title <span class="canvas-required">*</span>
                </label>
                <input type="text" name="e_resource[title]" placeholder="Enter the resource title" required class="canvas-form-input">
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">
                  URL <span class="canvas-required">*</span>
                </label>
                <input type="url" name="e_resource[url]" placeholder="https://example.com" required class="canvas-form-input">
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">
                  Discipline <span class="canvas-required">*</span>
                </label>
                <select name="e_resource[discipline]" required class="canvas-form-select">
                  <option value="">Select a discipline</option>
                  <option value="Agricultural Sciences">Agricultural Sciences</option>
                  <option value="Environmental Science">Environmental Science</option>
                  <option value="Forestry">Forestry</option>
                </select>
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">Description</label>
                <textarea name="e_resource[description]" rows="3" placeholder="Describe what this resource provides..." class="canvas-form-textarea"></textarea>
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">Tags</label>
                <input type="text" name="e_resource[tags]" placeholder="agriculture, research, database" id="modal_tags_input" class="canvas-form-input">
                <div class="canvas-form-help">Comma-separated keywords (e.g., agriculture, research, database)</div>
                <div class="canvas-tags-container">
                  <div class="canvas-tags-display" id="modal_tags_display"></div>
                </div>
              </div>

              <div class="canvas-metadata-section">
                <h4 class="canvas-metadata-title">Metadata Information</h4>
                
                <div class="canvas-form-row two-column">
                  <div>
                    <label class="canvas-form-label">Resource Type</label>
                    <select name="e_resource[metadata][type]" class="canvas-form-select">
                      <option value="">Select resource type</option>
                      <option value="Database">Database</option>
                      <option value="Journal Articles">Journal Articles</option>
                      <option value="Bibliographic Database">Bibliographic Database</option>
                      <option value="Statistical Database">Statistical Database</option>
                      <option value="Abstract Database">Abstract Database</option>
                      <option value="Citation Database">Citation Database</option>
                      <option value="Research Database">Research Database</option>
                      <option value="Environmental Data">Environmental Data</option>
                    </select>
                  </div>

                  <div>
                    <label class="canvas-form-label">Access Type</label>
                    <select name="e_resource[metadata][access]" class="canvas-form-select">
                      <option value="">Select access type</option>
                      <option value="Open Access">Open Access</option>
                      <option value="Subscription Required">Subscription Required</option>
                      <option value="Free Registration">Free Registration</option>
                      <option value="Institutional Access">Institutional Access</option>
                    </select>
                  </div>
                </div>

                <div class="canvas-form-row">
                  <label class="canvas-form-label">Update Frequency</label>
                  <select name="e_resource[metadata][updated]" class="canvas-form-select">
                    <option value="">Select update frequency</option>
                    <option value="Daily">Daily</option>
                    <option value="Weekly">Weekly</option>
                    <option value="Monthly">Monthly</option>
                    <option value="Quarterly">Quarterly</option>
                    <option value="Annually">Annually</option>
                    <option value="Regularly">Regularly</option>
                    <option value="Rarely">Rarely</option>
                  </select>
                </div>
              </div>
            </form>
          </div>

          <div class="canvas-modal-footer">
            <button type="button" class="canvas-btn canvas-btn-secondary" onclick="closeAddResourceModal()">Cancel</button>
            <button type="button" class="canvas-btn canvas-btn-primary" onclick="submitModalForm()">Create E-Resource</button>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('addResourceModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Setup event listeners
    setupModalEventListeners();
  }

  // Edit Modal Functions
  function openEditResourceModal(resourceId) {
    
    if (!resourceId) {
      alert('Error: No resource ID provided');
      return;
    }
    
    // Show loading state
    const loadingHTML = `
      <div id="editResourceModal" class="canvas-modal-overlay" style="display: flex;">
        <div class="canvas-modal-dialog">
          <div class="canvas-modal-header">
            <button type="button" class="canvas-modal-close" onclick="closeEditResourceModal()">&times;</button>
        </div>
      </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', loadingHTML);
    document.body.style.overflow = 'hidden';
    
    // Fetch resource data
    fetch(`/e-resources/${resourceId}.json`)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(resource => {
        
        // Remove loading modal
        const loadingModal = document.getElementById('editResourceModal');
        if (loadingModal) loadingModal.remove();
        
        // Create the actual edit modal
        createEditModalHTML(resource, resourceId);
        
        // Setup event listeners
        setupEditModalEventListeners();
        updateEditModalTagsDisplay();
      })
      .catch(error => {
        
        // Remove loading modal
        const loadingModal = document.getElementById('editResourceModal');
        if (loadingModal) loadingModal.remove();
        
        document.body.style.overflow = '';
        alert(`Error loading resource data: ${error.message}`);
      });
  }

  function createEditModalHTML(resource, resourceId) {
    const modalHTML = `
      <div id="editResourceModal" class="canvas-modal-overlay" style="display: flex;">
        <div class="canvas-modal-dialog">
          <div class="canvas-modal-header">
            <h3 class="canvas-modal-title">Edit E-Resource</h3>
            <button type="button" class="canvas-modal-close" onclick="closeEditResourceModal()">&times;</button>
          </div>
          
          <div class="canvas-modal-body">
            <form action="/e-resources/${resourceId}" method="post" id="editResourceForm">
              <input type="hidden" name="authenticity_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
              <input type="hidden" name="_method" value="patch">
              
              <div class="canvas-form-row">
                <label class="canvas-form-label">
                  Title <span class="canvas-required">*</span>
                </label>
                <input type="text" name="e_resource[title]" required class="canvas-form-input" value="${(resource.title || '').replace(/"/g, '&quot;')}">
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">
                  URL <span class="canvas-required">*</span>
                </label>
                <input type="url" name="e_resource[url]" required class="canvas-form-input" value="${(resource.url || '').replace(/"/g, '&quot;')}">
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">
                  Discipline <span class="canvas-required">*</span>
                </label>
                <select name="e_resource[discipline]" required class="canvas-form-input">
                  <option value="">Select a discipline</option>
                  <option value="Agricultural Sciences" ${resource.discipline === 'Agricultural Sciences' ? 'selected' : ''}>Agricultural Sciences</option>
                  <option value="Environmental Science" ${resource.discipline === 'Environmental Science' ? 'selected' : ''}>Environmental Science</option>
                  <option value="Forestry" ${resource.discipline === 'Forestry' ? 'selected' : ''}>Forestry</option>
                </select>
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">Description</label>
                <textarea name="e_resource[description]" rows="4" class="canvas-form-input">${(resource.description || '').replace(/</g, '&lt;').replace(/>/g, '&gt;')}</textarea>
              </div>

              <div class="canvas-form-row">
                <label class="canvas-form-label">Tags</label>
                <input type="text" name="e_resource[tags]" id="edit_modal_tags_input" class="canvas-form-input" value="${Array.isArray(resource.tags) ? resource.tags.join(', ') : ''}">
                <div class="canvas-form-help">Comma-separated keywords (e.g., agriculture, research, database)</div>
                <div class="canvas-tags-container">
                  <div class="canvas-tags-display" id="edit_modal_tags_display"></div>
                </div>
              </div>

              <div class="canvas-metadata-section">
                <h4 class="canvas-metadata-title">Metadata Information</h4>
                
                <div class="canvas-form-row two-column">
                  <div>
                    <label class="canvas-form-label">Resource Type</label>
                    <select name="e_resource[metadata][type]" class="canvas-form-input">
                      <option value="">Select resource type</option>
                      <option value="Database" ${resource.metadata?.type === 'Database' ? 'selected' : ''}>Database</option>
                      <option value="Journal Articles" ${resource.metadata?.type === 'Journal Articles' ? 'selected' : ''}>Journal Articles</option>
                      <option value="Bibliographic Database" ${resource.metadata?.type === 'Bibliographic Database' ? 'selected' : ''}>Bibliographic Database</option>
                      <option value="Statistical Database" ${resource.metadata?.type === 'Statistical Database' ? 'selected' : ''}>Statistical Database</option>
                      <option value="Abstract Database" ${resource.metadata?.type === 'Abstract Database' ? 'selected' : ''}>Abstract Database</option>
                      <option value="Citation Database" ${resource.metadata?.type === 'Citation Database' ? 'selected' : ''}>Citation Database</option>
                      <option value="Research Database" ${resource.metadata?.type === 'Research Database' ? 'selected' : ''}>Research Database</option>
                      <option value="Environmental Data" ${resource.metadata?.type === 'Environmental Data' ? 'selected' : ''}>Environmental Data</option>
                    </select>
                  </div>

                  <div>
                    <label class="canvas-form-label">Access Type</label>
                    <select name="e_resource[metadata][access]" class="canvas-form-input">
                      <option value="">Select access type</option>
                      <option value="Open Access" ${resource.metadata?.access === 'Open Access' ? 'selected' : ''}>Open Access</option>
                      <option value="Subscription Required" ${resource.metadata?.access === 'Subscription Required' ? 'selected' : ''}>Subscription Required</option>
                      <option value="Free Registration" ${resource.metadata?.access === 'Free Registration' ? 'selected' : ''}>Free Registration</option>
                      <option value="Institutional Access" ${resource.metadata?.access === 'Institutional Access' ? 'selected' : ''}>Institutional Access</option>
                    </select>
                  </div>
                </div>

                <div class="canvas-form-row">
                  <label class="canvas-form-label">Update Frequency</label>
                  <select name="e_resource[metadata][updated]" class="canvas-form-input">
                    <option value="">Select update frequency</option>
                    <option value="Daily" ${resource.metadata?.updated === 'Daily' ? 'selected' : ''}>Daily</option>
                    <option value="Weekly" ${resource.metadata?.updated === 'Weekly' ? 'selected' : ''}>Weekly</option>
                    <option value="Monthly" ${resource.metadata?.updated === 'Monthly' ? 'selected' : ''}>Monthly</option>
                    <option value="Quarterly" ${resource.metadata?.updated === 'Quarterly' ? 'selected' : ''}>Quarterly</option>
                    <option value="Annually" ${resource.metadata?.updated === 'Annually' ? 'selected' : ''}>Annually</option>
                    <option value="Regularly" ${resource.metadata?.updated === 'Regularly' ? 'selected' : ''}>Regularly</option>
                    <option value="Rarely" ${resource.metadata?.updated === 'Rarely' ? 'selected' : ''}>Rarely</option>
                  </select>
                </div>
              </div>
            </form>
          </div>

          <div class="canvas-modal-footer">
            <button type="button" class="canvas-btn canvas-btn-secondary" onclick="closeEditResourceModal()">Cancel</button>
            <button type="button" class="canvas-btn canvas-btn-primary" onclick="submitEditModalForm()">Update E-Resource</button>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
  }

  function setupModalEventListeners() {
    const modal = document.getElementById('addResourceModal');
    const tagsInput = document.getElementById('modal_tags_input');
    const form = document.getElementById('addResourceForm');

    // Close on outside click
    modal.addEventListener('click', function(e) {
      if (e.target === this) {
        closeAddResourceModal();
      }
    });

    // Tags input listener
    if (tagsInput) {
      tagsInput.addEventListener('input', updateModalTagsDisplay);
    }

    // Form submission
    if (form) {
      form.addEventListener('submit', function(e) {
        const submitButton = this.querySelector('button[type="submit"]');
        if (submitButton) {
          submitButton.disabled = true;
          submitButton.textContent = 'Creating...';
        }
      });
    }
  }

  function setupEditModalEventListeners() {
    const modal = document.getElementById('editResourceModal');
    const tagsInput = document.getElementById('edit_modal_tags_input');
    const form = document.getElementById('editResourceForm');

    // Close on outside click
    modal.addEventListener('click', function(e) {
      if (e.target === this) {
        closeEditResourceModal();
      }
    });

    // Tags input listener
    if (tagsInput) {
      tagsInput.addEventListener('input', updateEditModalTagsDisplay);
    }

    // Form submission
    if (form) {
      form.addEventListener('submit', function(e) {
        const submitButton = this.querySelector('button[type="submit"]');
        if (submitButton) {
          submitButton.disabled = true;
          submitButton.textContent = 'Updating...';
        }
      });
    }
  }

  function openAddResourceModal() {
    createAddResourceModal();
    
    const modal = document.getElementById('addResourceModal');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Focus first input
    setTimeout(() => {
      const firstInput = modal.querySelector('.canvas-form-input');
      if (firstInput) firstInput.focus();
    }, 100);
    
    updateModalTagsDisplay();
  }

  function closeAddResourceModal() {
    const modal = document.getElementById('addResourceModal');
    if (modal) {
      modal.remove();
    }
    document.body.style.overflow = '';
  }

  function closeEditResourceModal() {
    const modal = document.getElementById('editResourceModal');
    if (modal) {
      modal.remove();
    }
    document.body.style.overflow = '';
  }

  function submitModalForm() {
    const form = document.getElementById('addResourceForm');
    if (form) {
      const submitButton = document.querySelector('.canvas-btn-primary');
      if (submitButton) {
        submitButton.disabled = true;
        submitButton.textContent = 'Creating...';
      }
      form.submit();
    }
  }

  function submitEditModalForm() {
    const form = document.getElementById('editResourceForm');
    if (form) {
      const submitButton = document.querySelector('#editResourceModal .canvas-btn-primary');
      if (submitButton) {
        submitButton.disabled = true;
        submitButton.textContent = 'Updating...';
      }
      form.submit();
    }
  }

  // Tags Management
  function updateModalTagsDisplay() {
    const tagsInput = document.getElementById('modal_tags_input');
    const tagsDisplay = document.getElementById('modal_tags_display');
    
    if (!tagsInput || !tagsDisplay) return;
    
    const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    tagsDisplay.innerHTML = '';
    
    tags.forEach((tag, index) => {
      const tagElement = document.createElement('span');
      tagElement.className = 'canvas-tag-item';
      tagElement.innerHTML = `
        ${tag}
        <button type="button" class="canvas-tag-remove" onclick="removeModalTag(${index})">×</button>
      `;
      tagsDisplay.appendChild(tagElement);
    });
  }

  function updateEditModalTagsDisplay() {
    const tagsInput = document.getElementById('edit_modal_tags_input');
    const tagsDisplay = document.getElementById('edit_modal_tags_display');
    
    if (!tagsInput || !tagsDisplay) return;
    
    const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    tagsDisplay.innerHTML = '';
    
    tags.forEach((tag, index) => {
      const tagElement = document.createElement('span');
      tagElement.className = 'canvas-tag-item';
      tagElement.innerHTML = `
        ${tag}
        <button type="button" class="canvas-tag-remove" onclick="removeEditModalTag(${index})">×</button>
      `;
      tagsDisplay.appendChild(tagElement);
    });
  }

  function removeModalTag(index) {
    const tagsInput = document.getElementById('modal_tags_input');
    if (!tagsInput) return;
    
    const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    tags.splice(index, 1);
    tagsInput.value = tags.join(', ');
    updateModalTagsDisplay();
  }

  function removeEditModalTag(index) {
    const tagsInput = document.getElementById('edit_modal_tags_input');
    if (!tagsInput) return;
    
    const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    tags.splice(index, 1);
    tagsInput.value = tags.join(', ');
    updateEditModalTagsDisplay();
  }

  // Document Ready
  document.addEventListener('DOMContentLoaded', function () {
    updateEresourcesIconHighlight();

    window.addEventListener('popstate', updateEresourcesIconHighlight);
    window.addEventListener('pushstate', updateEresourcesIconHighlight);
    window.addEventListener('replacestate', updateEresourcesIconHighlight);

    const observer = new MutationObserver(() => {
      updateEresourcesIconHighlight();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Handle escape key to close modal
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const addModal = document.getElementById('addResourceModal');
        const editModal = document.getElementById('editResourceModal');
        
        if (addModal) {
          closeAddResourceModal();
        } else if (editModal) {
          closeEditResourceModal();
        }
      }
    });
  });
</script>

<style>
.e-resources-page {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  width: 100%;
  margin: 20px 0; 
  padding: 0 20px 30px 20px; 
  background: #fff;
  text-align: left;
  color: #033f1d;
}

.e-resources-title {
  color: #033f1d;
  font-size: 1.99rem;
  margin-bottom: 0.3em;
  border-bottom: 1px solid #C7CDD1;
  padding-bottom: 0.2em;
  font-weight: 700;
}

.intro-text {
  font-size: 1rem;
  margin-bottom: 1.2em;
  color: #05542a;
}

.search-bar {
  margin-bottom: 15px; 
}

.search-input {
  width: 98.7%;
  padding: 10px 14px; 
  font-size: 1rem;
  border: 2px solid #033f1d;
  border-radius: 6px;
  outline-color: #033f1d;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #05542a;
  box-shadow: 0 0 6px rgba(3, 63, 29, 0.4);
}

.discipline-section {
  margin-bottom: 30px;
  border-bottom: 1px solid #c5d6c7; 
  padding-bottom: 20px;
}

.discipline-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.discipline-section h2 {
  color: #033f1d;
  font-size: 1.7rem;
  border-left: 6px solid #033f1d;
  padding-left: 10px;
  margin-bottom: 0.8em;
  font-weight: 600;
}

.resource-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.resource-item {
  border: 1px solid #033f1d;
  border-radius: 6px;
  padding: 12px 16px; 
  margin-bottom: 12px; 
  background-color: #eaf4ea;
  transition: background-color 0.25s ease;
  line-height: 1.3;
}

.resource-item:hover {
  background-color: #d0e5d0;
}

.resource-item h3 a {
  color: #033f1d;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 6px;
  display: inline-block;
}

.resource-item p {
  margin: 4px 10px 4px 0;
  color: #04592a;
  font-size: 0.9rem;
  display: inline-block;
}

.resource-item p strong {
  font-weight: 600;
}

.resource-item p.tags {
  margin-top: 6px;
  margin-left: 0;
  padding-top: 4px;
}

.resource-item p.tags strong {
  margin-right: 8px;
  font-weight: 600;
}

.resource-item p.tags .tag-badge {
  display: inline-block;
  background-color: #d0e5d0;      
  color: #033f1d;                 
  border: 1px solid #033f1d;
  border-radius: 12px;
  padding: 3px 10px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-right: 6px;
  margin-bottom: 4px;
  cursor: default;
  user-select: none;
  transition: background-color 0.3s ease;
}

.resource-item p.tags .tag-badge:hover {
  background-color: #b5d1b4;
  border-color: #05542a;
}

.admin-panel {
  background: #f8f9fa;
  border: 2px solid #033f1d;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(3, 63, 29, 0.1);
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #033f1d;
}

.admin-title {
  color: #033f1d;
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.admin-badge {
  background-color: #033f1d;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.admin-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  display: inline-block;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: #033f1d;
  color: white;
}

.btn-primary:hover {
  background-color: #05542a;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
  color: white;
}

.stats-section {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
  border: 1px solid #dee2e6;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.3rem;
  font-weight: bold;
  color: #033f1d;
  display: block;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
}

.resource-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.resource-title-section a {
  flex: 1;
 
}
.resource-admin-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.btn-small {
  border: none;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  display: inline-block;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.2;
  box-sizing: border-box;
}

.btn-small.btn-primary {
  background-color: #033f1d;
  color: white;
}

.btn-small.btn-primary:hover {
  background-color: #05542a;
  color: white;
}

.btn-small.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-small.btn-danger:hover {
  background-color: #c82333;
  color: white;
}

.canvas-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.canvas-modal-dialog {
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.24);
  width: 600px;
  max-width: 90vw;
  height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0 20px;
}

.canvas-modal-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 20px;
  flex-shrink: 0;
}

.canvas-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3b45;
  margin: 0;
  line-height: 20px;
}

.canvas-modal-close {
  background: none;
  border: none;
  color: #687078;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-modal-close:hover {
  color: #2d3b45;
}

.canvas-modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  background: white;
  min-height: 0;
}

.canvas-modal-footer {
  background: #f5f5f5;
  border-top: 1px solid #e1e5e9;
  padding: 12px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  flex-shrink: 0;
}

.canvas-form-row {
  margin-bottom: 16px;
}

.canvas-form-row.two-column {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.canvas-form-label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: #2d3b45;
  margin-bottom: 4px;
  line-height: 16px;
}

.canvas-form-input{
  padding: 8px 12px;
  width: 97%;
}

.canvas-form-textarea,
.canvas-form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #c7cdd1;
  border-radius: 4px;
  font-size: 14px;
  line-height: 20px;
  color: #2d3b45;
  background: white;
  box-sizing: border-box;
  max-width: 100%;
}

.canvas-form-input:focus,
.canvas-form-textarea:focus,
.canvas-form-select:focus {
  border-color: #0084d1;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 132, 209, 0.25);
}

.canvas-form-textarea {
  min-height: 80px;
  resize: vertical;
}

.canvas-form-help {
  font-size: 12px;
  color: #687078;
  margin-top: 4px;
  line-height: 16px;
}

.canvas-required {
  color: #d2001f;
}

.canvas-tags-container {
  margin-top: 8px;
}

.canvas-tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  min-height: 32px;
  padding: 8px;
  border: 1px solid #c7cdd1;
  border-radius: 4px;
  background: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
}

.canvas-tag-item {
  background: #e1f5fe;
  color: #0d7a7e;
  border: 1px solid #b3e5fc;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  line-height: 16px;
}

.canvas-tag-remove {
  background: none;
  border: none;
  color: #0d7a7e;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  padding: 0;
  line-height: 1;
}

.canvas-tag-remove:hover {
  color: #065f63;
}

.canvas-metadata-section {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
}

.canvas-metadata-title {
  font-size: 14px;
  font-weight: 600;
  color: #2d3b45;
  margin: 0 0 12px 0;
}

.canvas-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  min-height: 36px;
  box-sizing: border-box;
}

.canvas-btn-primary {
  background: #215342;
  color: white;
  border-color: #215342;
}

.canvas-btn-primary:hover {
  background: #0e2d11;
  border-color: #0e2d11;
  color: white;
}

.canvas-btn-secondary {
  background: white;
  color: #2d3b45;
  border-color: #c7cdd1;
}

.canvas-btn-secondary:hover {
  background: #f5f5f5;
  border-color: #9ca6af;
  color: #2d3b45;
}

.ic-Layout-contentMain {
  padding: 0px 48px 48px 30px;
}

@media (max-width: 768px) {
  .resource-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .resource-admin-actions {
    margin-left: 0;
  }

  .canvas-modal-overlay {
    padding: 20px 10px;
  }

  .canvas-modal-dialog {
    width: 95vw;
    margin: 0;
    max-height: calc(100vh - 40px);
  }
  
  .canvas-modal-body {
    min-height: 0;
  }
  
  .canvas-form-row.two-column {
    grid-template-columns: 1fr;
  }
  
  .canvas-modal-footer {
    flex-direction: column-reverse;
  }
}
</style>