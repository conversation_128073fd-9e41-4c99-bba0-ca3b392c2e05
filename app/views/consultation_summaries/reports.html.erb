<%
  content_for :page_title, @page_title
  add_crumb "Consultation System", consultations_path
  add_crumb "Consultation Summaries", consultation_summaries_path
  add_crumb @page_title
%>

<div id="consultation-reports-container" class="consultation-reports-page">
  <div class="consultation-reports-content">
    <h1>Consultation Reports</h1>
    <p>Reports functionality is under development.</p>
    
    <div class="reports-data">
      <h3>Year: <%= @year %></h3>
      <h4>Total Consultations: <%= @total_consultations %></h4>
      
      <% if @concern_type_stats.present? %>
        <h4>Concern Type Statistics:</h4>
        <ul>
          <% @concern_type_stats.each do |stat| %>
            <li><%= stat[:concern_type] %>: <%= stat[:count] %></li>
          <% end %>
        </ul>
      <% end %>

      <% if @monthly_data.present? %>
        <h4>Monthly Summary for <%= @year %>:</h4>
        <% @monthly_data.each do |month, concern_types| %>
          <div class="monthly-summary">
            <h5><%= month.strftime("%B %Y") %></h5>
            <ul>
              <% concern_types.each do |concern_type, count| %>
                <li><%= concern_type.humanize %>: <%= count %></li>
              <% end %>
            </ul>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</div>
